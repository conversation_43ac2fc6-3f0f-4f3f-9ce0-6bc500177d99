cmake_minimum_required(VERSION 3.15)
project(camera_calibrate)

set(CMAKE_CXX_STANDARD 20)

find_package(OpenCV REQUIRED)
set(PCL_DIR /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/share/pcl-1.14)
find_package(PCL REQUIRED)

add_executable(${PROJECT_NAME} main.cpp)
target_link_libraries(${PROJECT_NAME} ${OpenCV_LIBS})

add_executable(verify_match_pointcloud verify_match_pointcloud.cpp)
target_include_directories(verify_match_pointcloud PRIVATE ${PCL_INCLUDE_DIRS})
target_link_libraries(verify_match_pointcloud ${OpenCV_LIBS} ${PCL_LIBRARIES})
target_compile_definitions(verify_match_pointcloud PRIVATE ${PCL_DEFINITIONS})
