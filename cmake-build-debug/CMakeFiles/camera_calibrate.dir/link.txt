/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/camera_calibrate.dir/main.cpp.o -o camera_calibrate  -Wl,-rpath,/usr/local/opencv_4_8_1/lib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_img_hash.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib
