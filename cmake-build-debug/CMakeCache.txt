# This is the CMakeCache file.
# For build in directory: /Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug
# It was generated by CMake: /Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/opt/homebrew/lib/cmake/Boost-1.85.0

Boost_FILESYSTEM_LIBRARY_RELEASE:STRING=/opt/homebrew/lib/libboost_filesystem-mt.dylib

//Path to a file.
Boost_INCLUDE_DIR:PATH=/opt/homebrew/include

Boost_IOSTREAMS_LIBRARY_RELEASE:STRING=/opt/homebrew/lib/libboost_iostreams-mt.dylib

Boost_SERIALIZATION_LIBRARY_RELEASE:STRING=/opt/homebrew/lib/libboost_serialization-mt.dylib

Boost_SYSTEM_LIBRARY_RELEASE:STRING=/opt/homebrew/lib/libboost_system-mt.dylib

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable colored diagnostics throughout.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/cc

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=camera_calibrate

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/opt/homebrew/share/eigen3/cmake

//Path to a program.
MACDEPLOYQT_EXECUTABLE:FILEPATH=/opt/homebrew/bin/macdeployqt

//Include for OpenGL on OS X
OPENGL_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework

//OpenGL library for OS X
OPENGL_gl_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework

//GLU library for OS X (usually same as OpenGL library)
OPENGL_glu_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/usr/local/opencv_4_8_1/lib/cmake/opencv4

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=NOTFOUND

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=NOTFOUND

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=NOTFOUND

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=NOTFOUND

//Path to a library.
OpenMP_libomp_LIBRARY:FILEPATH=OpenMP_libomp_LIBRARY-NOTFOUND

//Path to a file.
PCAP_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include

//Path to a library.
PCAP_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libpcap.tbd

//path to 2d headers
PCL_2D_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to common headers
PCL_COMMON_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_common library
PCL_COMMON_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib

//path to pcl_common library debug
PCL_COMMON_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib

//The directory containing a CMake configuration file for PCL.
PCL_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/share/pcl-1.14

//path to features headers
PCL_FEATURES_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_features library
PCL_FEATURES_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib

//path to pcl_features library debug
PCL_FEATURES_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib

//path to filters headers
PCL_FILTERS_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_filters library
PCL_FILTERS_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib

//path to pcl_filters library debug
PCL_FILTERS_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib

//path to geometry headers
PCL_GEOMETRY_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to io headers
PCL_IO_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_io library
PCL_IO_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib

//path to pcl_io library debug
PCL_IO_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib

//path to kdtree headers
PCL_KDTREE_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_kdtree library
PCL_KDTREE_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib

//path to pcl_kdtree library debug
PCL_KDTREE_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib

//path to keypoints headers
PCL_KEYPOINTS_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_keypoints library
PCL_KEYPOINTS_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib

//path to pcl_keypoints library debug
PCL_KEYPOINTS_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib

//path to ml headers
PCL_ML_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_ml library
PCL_ML_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib

//path to pcl_ml library debug
PCL_ML_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib

//path to octree headers
PCL_OCTREE_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_octree library
PCL_OCTREE_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib

//path to pcl_octree library debug
PCL_OCTREE_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib

//path to outofcore headers
PCL_OUTOFCORE_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_outofcore library
PCL_OUTOFCORE_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib

//path to pcl_outofcore library debug
PCL_OUTOFCORE_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib

//path to people headers
PCL_PEOPLE_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_people library
PCL_PEOPLE_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib

//path to pcl_people library debug
PCL_PEOPLE_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib

//path to recognition headers
PCL_RECOGNITION_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_recognition library
PCL_RECOGNITION_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib

//path to pcl_recognition library debug
PCL_RECOGNITION_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib

//path to registration headers
PCL_REGISTRATION_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_registration library
PCL_REGISTRATION_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib

//path to pcl_registration library debug
PCL_REGISTRATION_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib

//path to sample_consensus headers
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_sample_consensus library
PCL_SAMPLE_CONSENSUS_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib

//path to pcl_sample_consensus library debug
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib

//path to search headers
PCL_SEARCH_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_search library
PCL_SEARCH_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib

//path to pcl_search library debug
PCL_SEARCH_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib

//path to segmentation headers
PCL_SEGMENTATION_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_segmentation library
PCL_SEGMENTATION_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib

//path to pcl_segmentation library debug
PCL_SEGMENTATION_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib

//path to stereo headers
PCL_STEREO_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_stereo library
PCL_STEREO_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib

//path to pcl_stereo library debug
PCL_STEREO_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib

//path to surface headers
PCL_SURFACE_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_surface library
PCL_SURFACE_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib

//path to pcl_surface library debug
PCL_SURFACE_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib

//path to tracking headers
PCL_TRACKING_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_tracking library
PCL_TRACKING_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib

//path to pcl_tracking library debug
PCL_TRACKING_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib

//path to visualization headers
PCL_VISUALIZATION_INCLUDE_DIR:PATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14

//path to pcl_visualization library
PCL_VISUALIZATION_LIBRARY:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib

//path to pcl_visualization library debug
PCL_VISUALIZATION_LIBRARY_DEBUG:FILEPATH=/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/opt/homebrew/bin/pkg-config

//Path to a library.
PNG_LIBRARY_DEBUG:FILEPATH=PNG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
PNG_LIBRARY_RELEASE:FILEPATH=/opt/homebrew/lib/libpng.dylib

//Path to a file.
PNG_PNG_INCLUDE_DIR:PATH=/opt/homebrew/include

//Additional directories where find(Qt6 ...) host Qt components
// are searched
QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH:STRING=

//Additional directories where find(Qt6 ...) components are searched
QT_ADDITIONAL_PACKAGES_PREFIX_PATH:STRING=

//The directory containing a CMake configuration file for Qhull.
Qhull_DIR:PATH=/opt/homebrew/lib/cmake/Qhull

//The directory containing a CMake configuration file for Qt6CoreTools.
Qt6CoreTools_DIR:PATH=/opt/homebrew/lib/cmake/Qt6CoreTools

//The directory containing a CMake configuration file for Qt6Core.
Qt6Core_DIR:PATH=/opt/homebrew/lib/cmake/Qt6Core

//The directory containing a CMake configuration file for Qt6DBusTools.
Qt6DBusTools_DIR:PATH=/opt/homebrew/lib/cmake/Qt6DBusTools

//The directory containing a CMake configuration file for Qt6DBus.
Qt6DBus_DIR:PATH=/opt/homebrew/lib/cmake/Qt6DBus

//The directory containing a CMake configuration file for Qt6GuiTools.
Qt6GuiTools_DIR:PATH=/opt/homebrew/lib/cmake/Qt6GuiTools

//The directory containing a CMake configuration file for Qt6Gui.
Qt6Gui_DIR:PATH=/opt/homebrew/lib/cmake/Qt6Gui

//The directory containing a CMake configuration file for Qt6OpenGLWidgets.
Qt6OpenGLWidgets_DIR:PATH=/opt/homebrew/lib/cmake/Qt6OpenGLWidgets

//The directory containing a CMake configuration file for Qt6OpenGL.
Qt6OpenGL_DIR:PATH=/opt/homebrew/lib/cmake/Qt6OpenGL

//The directory containing a CMake configuration file for Qt6WidgetsTools.
Qt6WidgetsTools_DIR:PATH=/opt/homebrew/lib/cmake/Qt6WidgetsTools

//The directory containing a CMake configuration file for Qt6Widgets.
Qt6Widgets_DIR:PATH=/opt/homebrew/lib/cmake/Qt6Widgets

//The directory containing a CMake configuration file for Qt6.
Qt6_DIR:PATH=/opt/homebrew/lib/cmake/Qt6

//The directory containing VTKConfig.cmake
VTK_DIR:PATH=/usr/local/lib/cmake/vtk-9.3

//Path to a program.
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE:FILEPATH=Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND

//Path to a program.
Vulkan_GLSLC_EXECUTABLE:FILEPATH=Vulkan_GLSLC_EXECUTABLE-NOTFOUND

//Path to a file.
Vulkan_INCLUDE_DIR:PATH=Vulkan_INCLUDE_DIR-NOTFOUND

//Path to a library.
Vulkan_LIBRARY:FILEPATH=Vulkan_LIBRARY-NOTFOUND

//Path to a library.
WrapOpenGL_AGL:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/AGL.framework

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libz.tbd

//The directory containing a CMake configuration file for boost_atomic.
boost_atomic_DIR:PATH=/opt/homebrew/lib/cmake/boost_atomic-1.85.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=/opt/homebrew/lib/cmake/boost_filesystem-1.85.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/opt/homebrew/lib/cmake/boost_headers-1.85.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=/opt/homebrew/lib/cmake/boost_iostreams-1.85.0

//The directory containing a CMake configuration file for boost_serialization.
boost_serialization_DIR:PATH=/opt/homebrew/lib/cmake/boost_serialization-1.85.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=/opt/homebrew/lib/cmake/boost_system-1.85.0

//Value Computed by CMake
camera_calibrate_BINARY_DIR:STATIC=/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug

//Value Computed by CMake
camera_calibrate_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
camera_calibrate_SOURCE_DIR:STATIC=/Users/<USER>/work/Proj/camera_calibrate

//The directory containing a CMake configuration file for flann.
flann_DIR:PATH=/opt/homebrew/lib/cmake/flann

//Path to a file.
libusb_INCLUDE_DIR:PATH=/opt/homebrew/include

//Path to a library.
libusb_LIBRARIES:FILEPATH=/opt/homebrew/lib/libusb-1.0.dylib

//Path to a library.
pkgcfg_lib_PC_libusb_usb-1.0:FILEPATH=/opt/homebrew/Cellar/libusb/1.0.26/lib/libusb-1.0.dylib


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/work/Proj/camera_calibrate/cmake-build-debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/work/Proj/camera_calibrate
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/Users/<USER>/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/opt/homebrew/lib/cmake/Boost-1.85.0/BoostConfig.cmake][cfound components: system iostreams filesystem serialization ][v1.85.0(1.65.0)]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/usr/local/opencv_4_8_1][v4.8.1()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/System/Library/Frameworks/OpenGL.framework][cfound components: OpenGL ][v()]
//Details about finding PCL
FIND_PACKAGE_MESSAGE_DETAILS_PCL:INTERNAL=[pcl_common;pcl_kdtree;pcl_octree;pcl_search;pcl_sample_consensus;pcl_filters;pcl_io;pcl_features;pcl_ml;pcl_segmentation;pcl_visualization;pcl_surface;pcl_registration;pcl_keypoints;pcl_tracking;pcl_recognition;pcl_stereo;pcl_outofcore;pcl_people;Boost::system;Boost::iostreams;Boost::filesystem;Boost::serialization;VTK::ChartsCore;VTK::CommonColor;VTK::CommonComputationalGeometry;VTK::CommonCore;VTK::CommonDataModel;VTK::CommonExecutionModel;VTK::CommonMath;VTK::CommonMisc;VTK::CommonTransforms;VTK::FiltersCore;VTK::FiltersExtraction;VTK::FiltersGeneral;VTK::FiltersGeometry;VTK::FiltersModeling;VTK::FiltersSources;VTK::ImagingCore;VTK::ImagingSources;VTK::InteractionImage;VTK::InteractionStyle;VTK::InteractionWidgets;VTK::IOCore;VTK::IOGeometry;VTK::IOImage;VTK::IOLegacy;VTK::IOPLY;VTK::RenderingAnnotation;VTK::RenderingCore;VTK::RenderingContext2D;VTK::RenderingLOD;VTK::RenderingFreeType;VTK::ViewsCore;VTK::ViewsContext2D;VTK::RenderingOpenGL2;VTK::RenderingContextOpenGL2;VTK::GUISupportQt;FLANN::FLANN;QHULL::QHULL][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14;/opt/homebrew/include/eigen3;/opt/homebrew/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][v()]
//Details about finding PCL_2D
FIND_PACKAGE_MESSAGE_DETAILS_PCL_2D:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_COMMON
FIND_PACKAGE_MESSAGE_DETAILS_PCL_COMMON:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_FEATURES
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FEATURES:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_FILTERS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FILTERS:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_GEOMETRY
FIND_PACKAGE_MESSAGE_DETAILS_PCL_GEOMETRY:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_IO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IO:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_KDTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KDTREE:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_KEYPOINTS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KEYPOINTS:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_ML
FIND_PACKAGE_MESSAGE_DETAILS_PCL_ML:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_OCTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OCTREE:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_OUTOFCORE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OUTOFCORE:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_PEOPLE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_PEOPLE:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_RECOGNITION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_RECOGNITION:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_REGISTRATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_REGISTRATION:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_SAMPLE_CONSENSUS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SAMPLE_CONSENSUS:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_SEARCH
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEARCH:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_SEGMENTATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEGMENTATION:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_STEREO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_STEREO:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_SURFACE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SURFACE:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_TRACKING
FIND_PACKAGE_MESSAGE_DETAILS_PCL_TRACKING:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PCL_VISUALIZATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_VISUALIZATION:INTERNAL=[/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib][/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/include/pcl-1.14][v()]
//Details about finding PNG
FIND_PACKAGE_MESSAGE_DETAILS_PNG:INTERNAL=[/opt/homebrew/lib/libpng.dylib][/opt/homebrew/include][v1.6.43()]
//Details about finding Pcap
FIND_PACKAGE_MESSAGE_DETAILS_Pcap:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libpcap.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding WrapAtomic
FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic:INTERNAL=[1][v()]
//Details about finding WrapOpenGL
FIND_PACKAGE_MESSAGE_DETAILS_WrapOpenGL:INTERNAL=[ON][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/include][c ][v1.2.12()]
//Details about finding libusb
FIND_PACKAGE_MESSAGE_DETAILS_libusb:INTERNAL=[/opt/homebrew/lib/libusb-1.0.dylib][/opt/homebrew/include][v()]
//Test HAVE_STDATOMIC
HAVE_STDATOMIC:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp:INTERNAL=FALSE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_Xclang fopenmp:INTERNAL=FALSE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_libomp_LIBRARY
OpenMP_libomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_2D_INCLUDE_DIR
PCL_2D_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_INCLUDE_DIR
PCL_COMMON_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY
PCL_COMMON_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY_DEBUG
PCL_COMMON_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_INCLUDE_DIR
PCL_FEATURES_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY
PCL_FEATURES_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY_DEBUG
PCL_FEATURES_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_INCLUDE_DIR
PCL_FILTERS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY
PCL_FILTERS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY_DEBUG
PCL_FILTERS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_GEOMETRY_INCLUDE_DIR
PCL_GEOMETRY_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_INCLUDE_DIR
PCL_IO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY
PCL_IO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY_DEBUG
PCL_IO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_INCLUDE_DIR
PCL_KDTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY
PCL_KDTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY_DEBUG
PCL_KDTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_INCLUDE_DIR
PCL_KEYPOINTS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY
PCL_KEYPOINTS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY_DEBUG
PCL_KEYPOINTS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_INCLUDE_DIR
PCL_ML_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY
PCL_ML_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY_DEBUG
PCL_ML_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_INCLUDE_DIR
PCL_OCTREE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY
PCL_OCTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY_DEBUG
PCL_OCTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_INCLUDE_DIR
PCL_OUTOFCORE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY
PCL_OUTOFCORE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY_DEBUG
PCL_OUTOFCORE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_INCLUDE_DIR
PCL_PEOPLE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY
PCL_PEOPLE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY_DEBUG
PCL_PEOPLE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_INCLUDE_DIR
PCL_RECOGNITION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY
PCL_RECOGNITION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY_DEBUG
PCL_RECOGNITION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_INCLUDE_DIR
PCL_REGISTRATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY
PCL_REGISTRATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY_DEBUG
PCL_REGISTRATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_INCLUDE_DIR
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY
PCL_SAMPLE_CONSENSUS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_INCLUDE_DIR
PCL_SEARCH_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY
PCL_SEARCH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY_DEBUG
PCL_SEARCH_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_INCLUDE_DIR
PCL_SEGMENTATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY
PCL_SEGMENTATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY_DEBUG
PCL_SEGMENTATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_INCLUDE_DIR
PCL_STEREO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY
PCL_STEREO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY_DEBUG
PCL_STEREO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_INCLUDE_DIR
PCL_SURFACE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY
PCL_SURFACE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY_DEBUG
PCL_SURFACE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_INCLUDE_DIR
PCL_TRACKING_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY
PCL_TRACKING_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY_DEBUG
PCL_TRACKING_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_INCLUDE_DIR
PCL_VISUALIZATION_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY
PCL_VISUALIZATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY_DEBUG
PCL_VISUALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
PC_libusb_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0
PC_libusb_CFLAGS_I:INTERNAL=
PC_libusb_CFLAGS_OTHER:INTERNAL=
PC_libusb_FOUND:INTERNAL=1
PC_libusb_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/include
PC_libusb_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0
PC_libusb_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/libusb/1.0.26/lib;-lusb-1.0
PC_libusb_LDFLAGS_OTHER:INTERNAL=
PC_libusb_LIBDIR:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/lib
PC_libusb_LIBRARIES:INTERNAL=usb-1.0
PC_libusb_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/lib
PC_libusb_LIBS:INTERNAL=
PC_libusb_LIBS_L:INTERNAL=
PC_libusb_LIBS_OTHER:INTERNAL=
PC_libusb_LIBS_PATHS:INTERNAL=
PC_libusb_MODULE_NAME:INTERNAL=libusb-1.0
PC_libusb_PREFIX:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26
PC_libusb_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0
PC_libusb_STATIC_CFLAGS_I:INTERNAL=
PC_libusb_STATIC_CFLAGS_OTHER:INTERNAL=
PC_libusb_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/include/libusb-1.0
PC_libusb_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/libusb/1.0.26/lib;-lusb-1.0;-lobjc;-Wl,-framework,IOKit;-Wl,-framework,CoreFoundation;-Wl,-framework,Security
PC_libusb_STATIC_LDFLAGS_OTHER:INTERNAL=-Wl,-framework,IOKit;-Wl,-framework,CoreFoundation;-Wl,-framework,Security
PC_libusb_STATIC_LIBDIR:INTERNAL=
PC_libusb_STATIC_LIBRARIES:INTERNAL=usb-1.0;objc
PC_libusb_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/lib
PC_libusb_STATIC_LIBS:INTERNAL=
PC_libusb_STATIC_LIBS_L:INTERNAL=
PC_libusb_STATIC_LIBS_OTHER:INTERNAL=
PC_libusb_STATIC_LIBS_PATHS:INTERNAL=
PC_libusb_VERSION:INTERNAL=1.0.26
PC_libusb_libusb-1.0_INCLUDEDIR:INTERNAL=
PC_libusb_libusb-1.0_LIBDIR:INTERNAL=
PC_libusb_libusb-1.0_PREFIX:INTERNAL=
PC_libusb_libusb-1.0_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_DEBUG
PNG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_RELEASE
PNG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_PNG_INCLUDE_DIR
PNG_PNG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//Qt feature: abstractbutton (from target Qt6::Widgets)
QT_FEATURE_abstractbutton:INTERNAL=ON
//Qt feature: abstractslider (from target Qt6::Widgets)
QT_FEATURE_abstractslider:INTERNAL=ON
//Qt feature: accessibility (from target Qt6::Gui)
QT_FEATURE_accessibility:INTERNAL=ON
//Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)
QT_FEATURE_accessibility_atspi_bridge:INTERNAL=OFF
//Qt feature: action (from target Qt6::Gui)
QT_FEATURE_action:INTERNAL=ON
//Qt feature: aesni (from target Qt6::Core)
QT_FEATURE_aesni:INTERNAL=OFF
//Qt feature: alloca (from target Qt6::Core)
QT_FEATURE_alloca:INTERNAL=ON
//Qt feature: alloca_h (from target Qt6::Core)
QT_FEATURE_alloca_h:INTERNAL=ON
//Qt feature: alloca_malloc_h (from target Qt6::Core)
QT_FEATURE_alloca_malloc_h:INTERNAL=OFF
//Qt feature: android_style_assets (from target Qt6::Core)
QT_FEATURE_android_style_assets:INTERNAL=OFF
//Qt feature: animation (from target Qt6::Core)
QT_FEATURE_animation:INTERNAL=ON
//Qt feature: appstore_compliant (from target Qt6::Core)
QT_FEATURE_appstore_compliant:INTERNAL=OFF
//Qt feature: arm_crc32 (from target Qt6::Core)
QT_FEATURE_arm_crc32:INTERNAL=ON
//Qt feature: arm_crypto (from target Qt6::Core)
QT_FEATURE_arm_crypto:INTERNAL=ON
//Qt feature: avx (from target Qt6::Core)
QT_FEATURE_avx:INTERNAL=OFF
//Qt feature: avx2 (from target Qt6::Core)
QT_FEATURE_avx2:INTERNAL=OFF
//Qt feature: avx512bw (from target Qt6::Core)
QT_FEATURE_avx512bw:INTERNAL=OFF
//Qt feature: avx512cd (from target Qt6::Core)
QT_FEATURE_avx512cd:INTERNAL=OFF
//Qt feature: avx512dq (from target Qt6::Core)
QT_FEATURE_avx512dq:INTERNAL=OFF
//Qt feature: avx512er (from target Qt6::Core)
QT_FEATURE_avx512er:INTERNAL=OFF
//Qt feature: avx512f (from target Qt6::Core)
QT_FEATURE_avx512f:INTERNAL=OFF
//Qt feature: avx512ifma (from target Qt6::Core)
QT_FEATURE_avx512ifma:INTERNAL=OFF
//Qt feature: avx512pf (from target Qt6::Core)
QT_FEATURE_avx512pf:INTERNAL=OFF
//Qt feature: avx512vbmi (from target Qt6::Core)
QT_FEATURE_avx512vbmi:INTERNAL=OFF
//Qt feature: avx512vbmi2 (from target Qt6::Core)
QT_FEATURE_avx512vbmi2:INTERNAL=OFF
//Qt feature: avx512vl (from target Qt6::Core)
QT_FEATURE_avx512vl:INTERNAL=OFF
//Qt feature: backtrace (from target Qt6::Core)
QT_FEATURE_backtrace:INTERNAL=ON
//Qt feature: buttongroup (from target Qt6::Widgets)
QT_FEATURE_buttongroup:INTERNAL=ON
//Qt feature: calendarwidget (from target Qt6::Widgets)
QT_FEATURE_calendarwidget:INTERNAL=ON
//Qt feature: cborstreamreader (from target Qt6::Core)
QT_FEATURE_cborstreamreader:INTERNAL=ON
//Qt feature: cborstreamwriter (from target Qt6::Core)
QT_FEATURE_cborstreamwriter:INTERNAL=ON
//Qt feature: checkbox (from target Qt6::Widgets)
QT_FEATURE_checkbox:INTERNAL=ON
//Qt feature: clipboard (from target Qt6::Gui)
QT_FEATURE_clipboard:INTERNAL=ON
//Qt feature: clock_gettime (from target Qt6::Core)
QT_FEATURE_clock_gettime:INTERNAL=ON
//Qt feature: clock_monotonic (from target Qt6::Core)
QT_FEATURE_clock_monotonic:INTERNAL=OFF
//Qt feature: close_range (from target Qt6::Core)
QT_FEATURE_close_range:INTERNAL=OFF
//Qt feature: colordialog (from target Qt6::Widgets)
QT_FEATURE_colordialog:INTERNAL=ON
//Qt feature: colornames (from target Qt6::Gui)
QT_FEATURE_colornames:INTERNAL=ON
//Qt feature: columnview (from target Qt6::Widgets)
QT_FEATURE_columnview:INTERNAL=ON
//Qt feature: combobox (from target Qt6::Widgets)
QT_FEATURE_combobox:INTERNAL=ON
//Qt feature: commandlineparser (from target Qt6::Core)
QT_FEATURE_commandlineparser:INTERNAL=ON
//Qt feature: commandlinkbutton (from target Qt6::Widgets)
QT_FEATURE_commandlinkbutton:INTERNAL=ON
//Qt feature: completer (from target Qt6::Widgets)
QT_FEATURE_completer:INTERNAL=ON
//Qt feature: concatenatetablesproxymodel (from target Qt6::Core)
QT_FEATURE_concatenatetablesproxymodel:INTERNAL=ON
//Qt feature: concurrent (from target Qt6::Core)
QT_FEATURE_concurrent:INTERNAL=ON
//Qt feature: contextmenu (from target Qt6::Widgets)
QT_FEATURE_contextmenu:INTERNAL=ON
//Qt feature: cpp_winrt (from target Qt6::Core)
QT_FEATURE_cpp_winrt:INTERNAL=OFF
//Qt feature: cross_compile (from target Qt6::Core)
QT_FEATURE_cross_compile:INTERNAL=OFF
//Qt feature: cssparser (from target Qt6::Gui)
QT_FEATURE_cssparser:INTERNAL=ON
//Qt feature: ctf (from target Qt6::Core)
QT_FEATURE_ctf:INTERNAL=OFF
//Qt feature: cursor (from target Qt6::Gui)
QT_FEATURE_cursor:INTERNAL=ON
//Qt feature: cxx11_future (from target Qt6::Core)
QT_FEATURE_cxx11_future:INTERNAL=ON
//Qt feature: cxx17_filesystem (from target Qt6::Core)
QT_FEATURE_cxx17_filesystem:INTERNAL=ON
//Qt feature: cxx20 (from target Qt6::Core)
QT_FEATURE_cxx20:INTERNAL=OFF
//Qt feature: cxx2a (from target Qt6::Core)
QT_FEATURE_cxx2a:INTERNAL=OFF
//Qt feature: cxx2b (from target Qt6::Core)
QT_FEATURE_cxx2b:INTERNAL=OFF
//Qt feature: datawidgetmapper (from target Qt6::Widgets)
QT_FEATURE_datawidgetmapper:INTERNAL=ON
//Qt feature: datestring (from target Qt6::Core)
QT_FEATURE_datestring:INTERNAL=ON
//Qt feature: datetimeedit (from target Qt6::Widgets)
QT_FEATURE_datetimeedit:INTERNAL=ON
//Qt feature: datetimeparser (from target Qt6::Core)
QT_FEATURE_datetimeparser:INTERNAL=ON
//Qt feature: dbus (from target Qt6::Core)
QT_FEATURE_dbus:INTERNAL=ON
//Qt feature: dbus_linked (from target Qt6::Core)
QT_FEATURE_dbus_linked:INTERNAL=ON
//Qt feature: debug (from target Qt6::Core)
QT_FEATURE_debug:INTERNAL=OFF
//Qt feature: debug_and_release (from target Qt6::Core)
QT_FEATURE_debug_and_release:INTERNAL=OFF
//Qt feature: desktopservices (from target Qt6::Gui)
QT_FEATURE_desktopservices:INTERNAL=ON
//Qt feature: developer_build (from target Qt6::Core)
QT_FEATURE_developer_build:INTERNAL=OFF
//Qt feature: dial (from target Qt6::Widgets)
QT_FEATURE_dial:INTERNAL=ON
//Qt feature: dialog (from target Qt6::Widgets)
QT_FEATURE_dialog:INTERNAL=ON
//Qt feature: dialogbuttonbox (from target Qt6::Widgets)
QT_FEATURE_dialogbuttonbox:INTERNAL=ON
//Qt feature: direct2d (from target Qt6::Gui)
QT_FEATURE_direct2d:INTERNAL=OFF
//Qt feature: direct2d1_1 (from target Qt6::Gui)
QT_FEATURE_direct2d1_1:INTERNAL=OFF
//Qt feature: directfb (from target Qt6::Gui)
QT_FEATURE_directfb:INTERNAL=OFF
//Qt feature: directwrite (from target Qt6::Gui)
QT_FEATURE_directwrite:INTERNAL=OFF
//Qt feature: directwrite3 (from target Qt6::Gui)
QT_FEATURE_directwrite3:INTERNAL=OFF
//Qt feature: dladdr (from target Qt6::Core)
QT_FEATURE_dladdr:INTERNAL=ON
//Qt feature: dlopen (from target Qt6::Core)
QT_FEATURE_dlopen:INTERNAL=ON
//Qt feature: dockwidget (from target Qt6::Widgets)
QT_FEATURE_dockwidget:INTERNAL=ON
//Qt feature: doubleconversion (from target Qt6::Core)
QT_FEATURE_doubleconversion:INTERNAL=ON
//Qt feature: draganddrop (from target Qt6::Gui)
QT_FEATURE_draganddrop:INTERNAL=ON
//Qt feature: drm_atomic (from target Qt6::Gui)
QT_FEATURE_drm_atomic:INTERNAL=OFF
//Qt feature: dynamicgl (from target Qt6::Gui)
QT_FEATURE_dynamicgl:INTERNAL=OFF
//Qt feature: easingcurve (from target Qt6::Core)
QT_FEATURE_easingcurve:INTERNAL=ON
//Qt feature: effects (from target Qt6::Widgets)
QT_FEATURE_effects:INTERNAL=ON
//Qt feature: egl (from target Qt6::Gui)
QT_FEATURE_egl:INTERNAL=OFF
//Qt feature: egl_x11 (from target Qt6::Gui)
QT_FEATURE_egl_x11:INTERNAL=OFF
//Qt feature: eglfs (from target Qt6::Gui)
QT_FEATURE_eglfs:INTERNAL=OFF
//Qt feature: eglfs_brcm (from target Qt6::Gui)
QT_FEATURE_eglfs_brcm:INTERNAL=OFF
//Qt feature: eglfs_egldevice (from target Qt6::Gui)
QT_FEATURE_eglfs_egldevice:INTERNAL=OFF
//Qt feature: eglfs_gbm (from target Qt6::Gui)
QT_FEATURE_eglfs_gbm:INTERNAL=OFF
//Qt feature: eglfs_mali (from target Qt6::Gui)
QT_FEATURE_eglfs_mali:INTERNAL=OFF
//Qt feature: eglfs_openwfd (from target Qt6::Gui)
QT_FEATURE_eglfs_openwfd:INTERNAL=OFF
//Qt feature: eglfs_rcar (from target Qt6::Gui)
QT_FEATURE_eglfs_rcar:INTERNAL=OFF
//Qt feature: eglfs_viv (from target Qt6::Gui)
QT_FEATURE_eglfs_viv:INTERNAL=OFF
//Qt feature: eglfs_viv_wl (from target Qt6::Gui)
QT_FEATURE_eglfs_viv_wl:INTERNAL=OFF
//Qt feature: eglfs_vsp2 (from target Qt6::Gui)
QT_FEATURE_eglfs_vsp2:INTERNAL=OFF
//Qt feature: eglfs_x11 (from target Qt6::Gui)
QT_FEATURE_eglfs_x11:INTERNAL=OFF
//Qt feature: errormessage (from target Qt6::Widgets)
QT_FEATURE_errormessage:INTERNAL=ON
//Qt feature: etw (from target Qt6::Core)
QT_FEATURE_etw:INTERNAL=OFF
//Qt feature: evdev (from target Qt6::Gui)
QT_FEATURE_evdev:INTERNAL=OFF
//Qt feature: f16c (from target Qt6::Core)
QT_FEATURE_f16c:INTERNAL=OFF
//Qt feature: filedialog (from target Qt6::Widgets)
QT_FEATURE_filedialog:INTERNAL=ON
//Qt feature: filesystemiterator (from target Qt6::Core)
QT_FEATURE_filesystemiterator:INTERNAL=ON
//Qt feature: filesystemmodel (from target Qt6::Gui)
QT_FEATURE_filesystemmodel:INTERNAL=ON
//Qt feature: filesystemwatcher (from target Qt6::Core)
QT_FEATURE_filesystemwatcher:INTERNAL=ON
//Qt feature: fontcombobox (from target Qt6::Widgets)
QT_FEATURE_fontcombobox:INTERNAL=ON
//Qt feature: fontconfig (from target Qt6::Gui)
QT_FEATURE_fontconfig:INTERNAL=OFF
//Qt feature: fontdialog (from target Qt6::Widgets)
QT_FEATURE_fontdialog:INTERNAL=ON
//Qt feature: force_asserts (from target Qt6::Core)
QT_FEATURE_force_asserts:INTERNAL=OFF
//Qt feature: forkfd_pidfd (from target Qt6::Core)
QT_FEATURE_forkfd_pidfd:INTERNAL=OFF
//Qt feature: formlayout (from target Qt6::Widgets)
QT_FEATURE_formlayout:INTERNAL=ON
//Qt feature: framework (from target Qt6::Core)
QT_FEATURE_framework:INTERNAL=ON
//Qt feature: freetype (from target Qt6::Gui)
QT_FEATURE_freetype:INTERNAL=ON
//Qt feature: fscompleter (from target Qt6::Widgets)
QT_FEATURE_fscompleter:INTERNAL=ON
//Qt feature: futimens (from target Qt6::Core)
QT_FEATURE_futimens:INTERNAL=ON
//Qt feature: future (from target Qt6::Core)
QT_FEATURE_future:INTERNAL=ON
//Qt feature: gc_binaries (from target Qt6::Core)
QT_FEATURE_gc_binaries:INTERNAL=OFF
//Qt feature: gestures (from target Qt6::Core)
QT_FEATURE_gestures:INTERNAL=ON
//Qt feature: getauxval (from target Qt6::Core)
QT_FEATURE_getauxval:INTERNAL=OFF
//Qt feature: getentropy (from target Qt6::Core)
QT_FEATURE_getentropy:INTERNAL=ON
//Qt feature: gif (from target Qt6::Gui)
QT_FEATURE_gif:INTERNAL=ON
//Qt feature: glib (from target Qt6::Core)
QT_FEATURE_glib:INTERNAL=ON
//Qt feature: graphicseffect (from target Qt6::Widgets)
QT_FEATURE_graphicseffect:INTERNAL=ON
//Qt feature: graphicsframecapture (from target Qt6::Gui)
QT_FEATURE_graphicsframecapture:INTERNAL=ON
//Qt feature: graphicsview (from target Qt6::Widgets)
QT_FEATURE_graphicsview:INTERNAL=ON
//Qt feature: groupbox (from target Qt6::Widgets)
QT_FEATURE_groupbox:INTERNAL=ON
//Qt feature: gtk3 (from target Qt6::Widgets)
QT_FEATURE_gtk3:INTERNAL=OFF
//Qt feature: gui (from target Qt6::Core)
QT_FEATURE_gui:INTERNAL=ON
//Qt feature: harfbuzz (from target Qt6::Gui)
QT_FEATURE_harfbuzz:INTERNAL=ON
//Qt feature: highdpiscaling (from target Qt6::Gui)
QT_FEATURE_highdpiscaling:INTERNAL=ON
//Qt feature: hijricalendar (from target Qt6::Core)
QT_FEATURE_hijricalendar:INTERNAL=ON
//Qt feature: ico (from target Qt6::Gui)
QT_FEATURE_ico:INTERNAL=ON
//Qt feature: icu (from target Qt6::Core)
QT_FEATURE_icu:INTERNAL=ON
//Qt feature: identityproxymodel (from target Qt6::Core)
QT_FEATURE_identityproxymodel:INTERNAL=ON
//Qt feature: im (from target Qt6::Gui)
QT_FEATURE_im:INTERNAL=ON
//Qt feature: image_heuristic_mask (from target Qt6::Gui)
QT_FEATURE_image_heuristic_mask:INTERNAL=ON
//Qt feature: image_text (from target Qt6::Gui)
QT_FEATURE_image_text:INTERNAL=ON
//Qt feature: imageformat_bmp (from target Qt6::Gui)
QT_FEATURE_imageformat_bmp:INTERNAL=ON
//Qt feature: imageformat_jpeg (from target Qt6::Gui)
QT_FEATURE_imageformat_jpeg:INTERNAL=ON
//Qt feature: imageformat_png (from target Qt6::Gui)
QT_FEATURE_imageformat_png:INTERNAL=ON
//Qt feature: imageformat_ppm (from target Qt6::Gui)
QT_FEATURE_imageformat_ppm:INTERNAL=ON
//Qt feature: imageformat_xbm (from target Qt6::Gui)
QT_FEATURE_imageformat_xbm:INTERNAL=ON
//Qt feature: imageformat_xpm (from target Qt6::Gui)
QT_FEATURE_imageformat_xpm:INTERNAL=ON
//Qt feature: imageformatplugin (from target Qt6::Gui)
QT_FEATURE_imageformatplugin:INTERNAL=ON
//Qt feature: imageio_text_loading (from target Qt6::Gui)
QT_FEATURE_imageio_text_loading:INTERNAL=ON
//Qt feature: inotify (from target Qt6::Core)
QT_FEATURE_inotify:INTERNAL=OFF
//Qt feature: inputdialog (from target Qt6::Widgets)
QT_FEATURE_inputdialog:INTERNAL=ON
//Qt feature: integrityfb (from target Qt6::Gui)
QT_FEATURE_integrityfb:INTERNAL=OFF
//Qt feature: integrityhid (from target Qt6::Gui)
QT_FEATURE_integrityhid:INTERNAL=OFF
//Qt feature: intelcet (from target Qt6::Core)
QT_FEATURE_intelcet:INTERNAL=OFF
//Qt feature: islamiccivilcalendar (from target Qt6::Core)
QT_FEATURE_islamiccivilcalendar:INTERNAL=ON
//Qt feature: itemmodel (from target Qt6::Core)
QT_FEATURE_itemmodel:INTERNAL=ON
//Qt feature: itemviews (from target Qt6::Widgets)
QT_FEATURE_itemviews:INTERNAL=ON
//Qt feature: jalalicalendar (from target Qt6::Core)
QT_FEATURE_jalalicalendar:INTERNAL=ON
//Qt feature: journald (from target Qt6::Core)
QT_FEATURE_journald:INTERNAL=OFF
//Qt feature: jpeg (from target Qt6::Gui)
QT_FEATURE_jpeg:INTERNAL=ON
//Qt feature: keysequenceedit (from target Qt6::Widgets)
QT_FEATURE_keysequenceedit:INTERNAL=ON
//Qt feature: kms (from target Qt6::Gui)
QT_FEATURE_kms:INTERNAL=OFF
//Qt feature: label (from target Qt6::Widgets)
QT_FEATURE_label:INTERNAL=ON
//Qt feature: largefile (from target Qt6::Core)
QT_FEATURE_largefile:INTERNAL=ON
//Qt feature: lcdnumber (from target Qt6::Widgets)
QT_FEATURE_lcdnumber:INTERNAL=ON
//Qt feature: libinput (from target Qt6::Gui)
QT_FEATURE_libinput:INTERNAL=OFF
//Qt feature: libinput_axis_api (from target Qt6::Gui)
QT_FEATURE_libinput_axis_api:INTERNAL=OFF
//Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)
QT_FEATURE_libinput_hires_wheel_support:INTERNAL=OFF
//Qt feature: library (from target Qt6::Core)
QT_FEATURE_library:INTERNAL=ON
//Qt feature: libudev (from target Qt6::Core)
QT_FEATURE_libudev:INTERNAL=OFF
//Qt feature: lineedit (from target Qt6::Widgets)
QT_FEATURE_lineedit:INTERNAL=ON
//Qt feature: linkat (from target Qt6::Core)
QT_FEATURE_linkat:INTERNAL=OFF
//Qt feature: linuxfb (from target Qt6::Gui)
QT_FEATURE_linuxfb:INTERNAL=OFF
//Qt feature: listview (from target Qt6::Widgets)
QT_FEATURE_listview:INTERNAL=ON
//Qt feature: listwidget (from target Qt6::Widgets)
QT_FEATURE_listwidget:INTERNAL=ON
//Qt feature: lttng (from target Qt6::Core)
QT_FEATURE_lttng:INTERNAL=OFF
//Qt feature: mainwindow (from target Qt6::Widgets)
QT_FEATURE_mainwindow:INTERNAL=ON
//Qt feature: mdiarea (from target Qt6::Widgets)
QT_FEATURE_mdiarea:INTERNAL=ON
//Qt feature: menu (from target Qt6::Widgets)
QT_FEATURE_menu:INTERNAL=ON
//Qt feature: menubar (from target Qt6::Widgets)
QT_FEATURE_menubar:INTERNAL=ON
//Qt feature: messagebox (from target Qt6::Widgets)
QT_FEATURE_messagebox:INTERNAL=ON
//Qt feature: mimetype (from target Qt6::Core)
QT_FEATURE_mimetype:INTERNAL=ON
//Qt feature: mimetype_database (from target Qt6::Core)
QT_FEATURE_mimetype_database:INTERNAL=ON
//Qt feature: mips_dsp (from target Qt6::Core)
QT_FEATURE_mips_dsp:INTERNAL=OFF
//Qt feature: mips_dspr2 (from target Qt6::Core)
QT_FEATURE_mips_dspr2:INTERNAL=OFF
//Qt feature: movie (from target Qt6::Gui)
QT_FEATURE_movie:INTERNAL=ON
//Qt feature: mtdev (from target Qt6::Gui)
QT_FEATURE_mtdev:INTERNAL=OFF
//Qt feature: multiprocess (from target Qt6::Gui)
QT_FEATURE_multiprocess:INTERNAL=ON
//Qt feature: neon (from target Qt6::Core)
QT_FEATURE_neon:INTERNAL=ON
//Qt feature: network (from target Qt6::Core)
QT_FEATURE_network:INTERNAL=ON
//Qt feature: no_direct_extern_access (from target Qt6::Core)
QT_FEATURE_no_direct_extern_access:INTERNAL=OFF
//Qt feature: opengl (from target Qt6::Gui)
QT_FEATURE_opengl:INTERNAL=ON
//Qt feature: opengles2 (from target Qt6::Gui)
QT_FEATURE_opengles2:INTERNAL=OFF
//Qt feature: opengles3 (from target Qt6::Gui)
QT_FEATURE_opengles3:INTERNAL=OFF
//Qt feature: opengles31 (from target Qt6::Gui)
QT_FEATURE_opengles31:INTERNAL=OFF
//Qt feature: opengles32 (from target Qt6::Gui)
QT_FEATURE_opengles32:INTERNAL=OFF
//Qt feature: openssl (from target Qt6::Core)
QT_FEATURE_openssl:INTERNAL=ON
//Qt feature: openssl_hash (from target Qt6::Core)
QT_FEATURE_openssl_hash:INTERNAL=OFF
//Qt feature: openssl_linked (from target Qt6::Core)
QT_FEATURE_openssl_linked:INTERNAL=OFF
//Qt feature: opensslv11 (from target Qt6::Core)
QT_FEATURE_opensslv11:INTERNAL=OFF
//Qt feature: opensslv30 (from target Qt6::Core)
QT_FEATURE_opensslv30:INTERNAL=ON
//Qt feature: openvg (from target Qt6::Gui)
QT_FEATURE_openvg:INTERNAL=OFF
//Qt feature: pcre2 (from target Qt6::Core)
QT_FEATURE_pcre2:INTERNAL=ON
//Qt feature: pdf (from target Qt6::Gui)
QT_FEATURE_pdf:INTERNAL=ON
//Qt feature: permissions (from target Qt6::Core)
QT_FEATURE_permissions:INTERNAL=ON
//Qt feature: picture (from target Qt6::Gui)
QT_FEATURE_picture:INTERNAL=ON
//Qt feature: pkg_config (from target Qt6::Core)
QT_FEATURE_pkg_config:INTERNAL=ON
//Qt feature: plugin_manifest (from target Qt6::Core)
QT_FEATURE_plugin_manifest:INTERNAL=ON
//Qt feature: png (from target Qt6::Gui)
QT_FEATURE_png:INTERNAL=ON
//Qt feature: poll_exit_on_error (from target Qt6::Core)
QT_FEATURE_poll_exit_on_error:INTERNAL=OFF
//Qt feature: poll_poll (from target Qt6::Core)
QT_FEATURE_poll_poll:INTERNAL=ON
//Qt feature: poll_pollts (from target Qt6::Core)
QT_FEATURE_poll_pollts:INTERNAL=OFF
//Qt feature: poll_ppoll (from target Qt6::Core)
QT_FEATURE_poll_ppoll:INTERNAL=OFF
//Qt feature: poll_select (from target Qt6::Core)
QT_FEATURE_poll_select:INTERNAL=OFF
//Qt feature: posix_fallocate (from target Qt6::Core)
QT_FEATURE_posix_fallocate:INTERNAL=OFF
//Qt feature: posix_sem (from target Qt6::Core)
QT_FEATURE_posix_sem:INTERNAL=ON
//Qt feature: posix_shm (from target Qt6::Core)
QT_FEATURE_posix_shm:INTERNAL=ON
//Qt feature: precompile_header (from target Qt6::Core)
QT_FEATURE_precompile_header:INTERNAL=ON
//Qt feature: printsupport (from target Qt6::Core)
QT_FEATURE_printsupport:INTERNAL=ON
//Qt feature: private_tests (from target Qt6::Core)
QT_FEATURE_private_tests:INTERNAL=OFF
//Qt feature: process (from target Qt6::Core)
QT_FEATURE_process:INTERNAL=ON
//Qt feature: processenvironment (from target Qt6::Core)
QT_FEATURE_processenvironment:INTERNAL=ON
//Qt feature: progressbar (from target Qt6::Widgets)
QT_FEATURE_progressbar:INTERNAL=ON
//Qt feature: progressdialog (from target Qt6::Widgets)
QT_FEATURE_progressdialog:INTERNAL=ON
//Qt feature: proxymodel (from target Qt6::Core)
QT_FEATURE_proxymodel:INTERNAL=ON
//Qt feature: pushbutton (from target Qt6::Widgets)
QT_FEATURE_pushbutton:INTERNAL=ON
//Qt feature: qqnx_imf (from target Qt6::Gui)
QT_FEATURE_qqnx_imf:INTERNAL=OFF
//Qt feature: qqnx_pps (from target Qt6::Core)
QT_FEATURE_qqnx_pps:INTERNAL=OFF
//Qt feature: qt_framework (from target Qt6::Core)
QT_FEATURE_qt_framework:INTERNAL=ON
//Qt feature: radiobutton (from target Qt6::Widgets)
QT_FEATURE_radiobutton:INTERNAL=ON
//Qt feature: raster_64bit (from target Qt6::Gui)
QT_FEATURE_raster_64bit:INTERNAL=ON
//Qt feature: raster_fp (from target Qt6::Gui)
QT_FEATURE_raster_fp:INTERNAL=ON
//Qt feature: rdrnd (from target Qt6::Core)
QT_FEATURE_rdrnd:INTERNAL=OFF
//Qt feature: rdseed (from target Qt6::Core)
QT_FEATURE_rdseed:INTERNAL=OFF
//Qt feature: reduce_exports (from target Qt6::Core)
QT_FEATURE_reduce_exports:INTERNAL=ON
//Qt feature: reduce_relocations (from target Qt6::Core)
QT_FEATURE_reduce_relocations:INTERNAL=OFF
//Qt feature: regularexpression (from target Qt6::Core)
QT_FEATURE_regularexpression:INTERNAL=ON
//Qt feature: relocatable (from target Qt6::Core)
QT_FEATURE_relocatable:INTERNAL=OFF
//Qt feature: renameat2 (from target Qt6::Core)
QT_FEATURE_renameat2:INTERNAL=OFF
//Qt feature: resizehandler (from target Qt6::Widgets)
QT_FEATURE_resizehandler:INTERNAL=ON
//Qt feature: rpath (from target Qt6::Core)
QT_FEATURE_rpath:INTERNAL=ON
//Qt feature: rubberband (from target Qt6::Widgets)
QT_FEATURE_rubberband:INTERNAL=ON
//Qt feature: scrollarea (from target Qt6::Widgets)
QT_FEATURE_scrollarea:INTERNAL=ON
//Qt feature: scrollbar (from target Qt6::Widgets)
QT_FEATURE_scrollbar:INTERNAL=ON
//Qt feature: scroller (from target Qt6::Widgets)
QT_FEATURE_scroller:INTERNAL=ON
//Qt feature: separate_debug_info (from target Qt6::Core)
QT_FEATURE_separate_debug_info:INTERNAL=OFF
//Qt feature: sessionmanager (from target Qt6::Gui)
QT_FEATURE_sessionmanager:INTERNAL=ON
//Qt feature: settings (from target Qt6::Core)
QT_FEATURE_settings:INTERNAL=ON
//Qt feature: sha3_fast (from target Qt6::Core)
QT_FEATURE_sha3_fast:INTERNAL=ON
//Qt feature: shani (from target Qt6::Core)
QT_FEATURE_shani:INTERNAL=OFF
//Qt feature: shared (from target Qt6::Core)
QT_FEATURE_shared:INTERNAL=ON
//Qt feature: sharedmemory (from target Qt6::Core)
QT_FEATURE_sharedmemory:INTERNAL=ON
//Qt feature: shortcut (from target Qt6::Core)
QT_FEATURE_shortcut:INTERNAL=ON
//Qt feature: signaling_nan (from target Qt6::Core)
QT_FEATURE_signaling_nan:INTERNAL=ON
//Qt feature: simulator_and_device (from target Qt6::Core)
QT_FEATURE_simulator_and_device:INTERNAL=OFF
//Qt feature: sizegrip (from target Qt6::Widgets)
QT_FEATURE_sizegrip:INTERNAL=ON
//Qt feature: slider (from target Qt6::Widgets)
QT_FEATURE_slider:INTERNAL=ON
//Qt feature: slog2 (from target Qt6::Core)
QT_FEATURE_slog2:INTERNAL=OFF
//Qt feature: sortfilterproxymodel (from target Qt6::Core)
QT_FEATURE_sortfilterproxymodel:INTERNAL=ON
//Qt feature: spinbox (from target Qt6::Widgets)
QT_FEATURE_spinbox:INTERNAL=ON
//Qt feature: splashscreen (from target Qt6::Widgets)
QT_FEATURE_splashscreen:INTERNAL=ON
//Qt feature: splitter (from target Qt6::Widgets)
QT_FEATURE_splitter:INTERNAL=ON
//Qt feature: sql (from target Qt6::Core)
QT_FEATURE_sql:INTERNAL=ON
//Qt feature: sse2 (from target Qt6::Core)
QT_FEATURE_sse2:INTERNAL=OFF
//Qt feature: sse3 (from target Qt6::Core)
QT_FEATURE_sse3:INTERNAL=OFF
//Qt feature: sse4_1 (from target Qt6::Core)
QT_FEATURE_sse4_1:INTERNAL=OFF
//Qt feature: sse4_2 (from target Qt6::Core)
QT_FEATURE_sse4_2:INTERNAL=OFF
//Qt feature: ssse3 (from target Qt6::Core)
QT_FEATURE_ssse3:INTERNAL=OFF
//Qt feature: stack_protector_strong (from target Qt6::Core)
QT_FEATURE_stack_protector_strong:INTERNAL=OFF
//Qt feature: stackedwidget (from target Qt6::Widgets)
QT_FEATURE_stackedwidget:INTERNAL=ON
//Qt feature: standarditemmodel (from target Qt6::Gui)
QT_FEATURE_standarditemmodel:INTERNAL=ON
//Qt feature: static (from target Qt6::Core)
QT_FEATURE_static:INTERNAL=OFF
//Qt feature: statusbar (from target Qt6::Widgets)
QT_FEATURE_statusbar:INTERNAL=ON
//Qt feature: statustip (from target Qt6::Widgets)
QT_FEATURE_statustip:INTERNAL=ON
//Qt feature: std_atomic64 (from target Qt6::Core)
QT_FEATURE_std_atomic64:INTERNAL=ON
//Qt feature: stdlib_libcpp (from target Qt6::Core)
QT_FEATURE_stdlib_libcpp:INTERNAL=OFF
//Qt feature: stringlistmodel (from target Qt6::Core)
QT_FEATURE_stringlistmodel:INTERNAL=ON
//Qt feature: style_android (from target Qt6::Widgets)
QT_FEATURE_style_android:INTERNAL=OFF
//Qt feature: style_fusion (from target Qt6::Widgets)
QT_FEATURE_style_fusion:INTERNAL=ON
//Qt feature: style_mac (from target Qt6::Widgets)
QT_FEATURE_style_mac:INTERNAL=ON
//Qt feature: style_stylesheet (from target Qt6::Widgets)
QT_FEATURE_style_stylesheet:INTERNAL=ON
//Qt feature: style_windows (from target Qt6::Widgets)
QT_FEATURE_style_windows:INTERNAL=ON
//Qt feature: style_windows11 (from target Qt6::Widgets)
QT_FEATURE_style_windows11:INTERNAL=OFF
//Qt feature: style_windowsvista (from target Qt6::Widgets)
QT_FEATURE_style_windowsvista:INTERNAL=OFF
//Qt feature: syntaxhighlighter (from target Qt6::Widgets)
QT_FEATURE_syntaxhighlighter:INTERNAL=ON
//Qt feature: syslog (from target Qt6::Core)
QT_FEATURE_syslog:INTERNAL=OFF
//Qt feature: system_doubleconversion (from target Qt6::Core)
QT_FEATURE_system_doubleconversion:INTERNAL=ON
//Qt feature: system_freetype (from target Qt6::Gui)
QT_FEATURE_system_freetype:INTERNAL=ON
//Qt feature: system_harfbuzz (from target Qt6::Gui)
QT_FEATURE_system_harfbuzz:INTERNAL=ON
//Qt feature: system_jpeg (from target Qt6::Gui)
QT_FEATURE_system_jpeg:INTERNAL=ON
//Qt feature: system_libb2 (from target Qt6::Core)
QT_FEATURE_system_libb2:INTERNAL=ON
//Qt feature: system_pcre2 (from target Qt6::Core)
QT_FEATURE_system_pcre2:INTERNAL=ON
//Qt feature: system_png (from target Qt6::Gui)
QT_FEATURE_system_png:INTERNAL=ON
//Qt feature: system_textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_system_textmarkdownreader:INTERNAL=ON
//Qt feature: system_xcb_xinput (from target Qt6::Gui)
QT_FEATURE_system_xcb_xinput:INTERNAL=OFF
//Qt feature: system_zlib (from target Qt6::Core)
QT_FEATURE_system_zlib:INTERNAL=ON
//Qt feature: systemsemaphore (from target Qt6::Core)
QT_FEATURE_systemsemaphore:INTERNAL=ON
//Qt feature: systemtrayicon (from target Qt6::Gui)
QT_FEATURE_systemtrayicon:INTERNAL=ON
//Qt feature: sysv_sem (from target Qt6::Core)
QT_FEATURE_sysv_sem:INTERNAL=ON
//Qt feature: sysv_shm (from target Qt6::Core)
QT_FEATURE_sysv_shm:INTERNAL=ON
//Qt feature: tabbar (from target Qt6::Widgets)
QT_FEATURE_tabbar:INTERNAL=ON
//Qt feature: tabletevent (from target Qt6::Gui)
QT_FEATURE_tabletevent:INTERNAL=ON
//Qt feature: tableview (from target Qt6::Widgets)
QT_FEATURE_tableview:INTERNAL=ON
//Qt feature: tablewidget (from target Qt6::Widgets)
QT_FEATURE_tablewidget:INTERNAL=ON
//Qt feature: tabwidget (from target Qt6::Widgets)
QT_FEATURE_tabwidget:INTERNAL=ON
//Qt feature: temporaryfile (from target Qt6::Core)
QT_FEATURE_temporaryfile:INTERNAL=ON
//Qt feature: testlib (from target Qt6::Core)
QT_FEATURE_testlib:INTERNAL=ON
//Qt feature: textbrowser (from target Qt6::Widgets)
QT_FEATURE_textbrowser:INTERNAL=ON
//Qt feature: textdate (from target Qt6::Core)
QT_FEATURE_textdate:INTERNAL=ON
//Qt feature: textedit (from target Qt6::Widgets)
QT_FEATURE_textedit:INTERNAL=ON
//Qt feature: texthtmlparser (from target Qt6::Gui)
QT_FEATURE_texthtmlparser:INTERNAL=ON
//Qt feature: textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_textmarkdownreader:INTERNAL=ON
//Qt feature: textmarkdownwriter (from target Qt6::Gui)
QT_FEATURE_textmarkdownwriter:INTERNAL=ON
//Qt feature: textodfwriter (from target Qt6::Gui)
QT_FEATURE_textodfwriter:INTERNAL=ON
//Qt feature: thread (from target Qt6::Core)
QT_FEATURE_thread:INTERNAL=ON
//Qt feature: timezone (from target Qt6::Core)
QT_FEATURE_timezone:INTERNAL=ON
//Qt feature: toolbar (from target Qt6::Widgets)
QT_FEATURE_toolbar:INTERNAL=ON
//Qt feature: toolbox (from target Qt6::Widgets)
QT_FEATURE_toolbox:INTERNAL=ON
//Qt feature: toolbutton (from target Qt6::Widgets)
QT_FEATURE_toolbutton:INTERNAL=ON
//Qt feature: tooltip (from target Qt6::Widgets)
QT_FEATURE_tooltip:INTERNAL=ON
//Qt feature: translation (from target Qt6::Core)
QT_FEATURE_translation:INTERNAL=ON
//Qt feature: transposeproxymodel (from target Qt6::Core)
QT_FEATURE_transposeproxymodel:INTERNAL=ON
//Qt feature: treeview (from target Qt6::Widgets)
QT_FEATURE_treeview:INTERNAL=ON
//Qt feature: treewidget (from target Qt6::Widgets)
QT_FEATURE_treewidget:INTERNAL=ON
//Qt feature: tslib (from target Qt6::Gui)
QT_FEATURE_tslib:INTERNAL=OFF
//Qt feature: tuiotouch (from target Qt6::Gui)
QT_FEATURE_tuiotouch:INTERNAL=ON
//Qt feature: undocommand (from target Qt6::Gui)
QT_FEATURE_undocommand:INTERNAL=ON
//Qt feature: undogroup (from target Qt6::Gui)
QT_FEATURE_undogroup:INTERNAL=ON
//Qt feature: undostack (from target Qt6::Gui)
QT_FEATURE_undostack:INTERNAL=ON
//Qt feature: undoview (from target Qt6::Widgets)
QT_FEATURE_undoview:INTERNAL=ON
//Qt feature: use_bfd_linker (from target Qt6::Core)
QT_FEATURE_use_bfd_linker:INTERNAL=OFF
//Qt feature: use_gold_linker (from target Qt6::Core)
QT_FEATURE_use_gold_linker:INTERNAL=OFF
//Qt feature: use_lld_linker (from target Qt6::Core)
QT_FEATURE_use_lld_linker:INTERNAL=OFF
//Qt feature: use_mold_linker (from target Qt6::Core)
QT_FEATURE_use_mold_linker:INTERNAL=OFF
//Qt feature: vaes (from target Qt6::Core)
QT_FEATURE_vaes:INTERNAL=OFF
//Qt feature: validator (from target Qt6::Gui)
QT_FEATURE_validator:INTERNAL=ON
//Qt feature: vkgen (from target Qt6::Gui)
QT_FEATURE_vkgen:INTERNAL=ON
//Qt feature: vkkhrdisplay (from target Qt6::Gui)
QT_FEATURE_vkkhrdisplay:INTERNAL=OFF
//Qt feature: vnc (from target Qt6::Gui)
QT_FEATURE_vnc:INTERNAL=OFF
//Qt feature: vsp2 (from target Qt6::Gui)
QT_FEATURE_vsp2:INTERNAL=OFF
//Qt feature: vulkan (from target Qt6::Gui)
QT_FEATURE_vulkan:INTERNAL=ON
//Qt feature: wasm_exceptions (from target Qt6::Core)
QT_FEATURE_wasm_exceptions:INTERNAL=OFF
//Qt feature: wasm_simd128 (from target Qt6::Core)
QT_FEATURE_wasm_simd128:INTERNAL=OFF
//Qt feature: wayland (from target Qt6::Gui)
QT_FEATURE_wayland:INTERNAL=OFF
//Qt feature: whatsthis (from target Qt6::Gui)
QT_FEATURE_whatsthis:INTERNAL=ON
//Qt feature: wheelevent (from target Qt6::Gui)
QT_FEATURE_wheelevent:INTERNAL=ON
//Qt feature: widgets (from target Qt6::Core)
QT_FEATURE_widgets:INTERNAL=ON
//Qt feature: widgettextcontrol (from target Qt6::Widgets)
QT_FEATURE_widgettextcontrol:INTERNAL=ON
//Qt feature: wizard (from target Qt6::Widgets)
QT_FEATURE_wizard:INTERNAL=ON
//Qt feature: x86intrin (from target Qt6::Core)
QT_FEATURE_x86intrin:INTERNAL=OFF
//Qt feature: xcb (from target Qt6::Gui)
QT_FEATURE_xcb:INTERNAL=OFF
//Qt feature: xcb_egl_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_egl_plugin:INTERNAL=OFF
//Qt feature: xcb_glx (from target Qt6::Gui)
QT_FEATURE_xcb_glx:INTERNAL=OFF
//Qt feature: xcb_glx_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_glx_plugin:INTERNAL=OFF
//Qt feature: xcb_native_painting (from target Qt6::Gui)
QT_FEATURE_xcb_native_painting:INTERNAL=OFF
//Qt feature: xcb_sm (from target Qt6::Gui)
QT_FEATURE_xcb_sm:INTERNAL=OFF
//Qt feature: xcb_xlib (from target Qt6::Gui)
QT_FEATURE_xcb_xlib:INTERNAL=OFF
//Qt feature: xkbcommon (from target Qt6::Gui)
QT_FEATURE_xkbcommon:INTERNAL=OFF
//Qt feature: xkbcommon_x11 (from target Qt6::Gui)
QT_FEATURE_xkbcommon_x11:INTERNAL=OFF
//Qt feature: xlib (from target Qt6::Gui)
QT_FEATURE_xlib:INTERNAL=OFF
//Qt feature: xml (from target Qt6::Core)
QT_FEATURE_xml:INTERNAL=ON
//Qt feature: xmlstream (from target Qt6::Core)
QT_FEATURE_xmlstream:INTERNAL=ON
//Qt feature: xmlstreamreader (from target Qt6::Core)
QT_FEATURE_xmlstreamreader:INTERNAL=ON
//Qt feature: xmlstreamwriter (from target Qt6::Core)
QT_FEATURE_xmlstreamwriter:INTERNAL=ON
//Qt feature: xrender (from target Qt6::Gui)
QT_FEATURE_xrender:INTERNAL=OFF
//Qt feature: zstd (from target Qt6::Core)
QT_FEATURE_zstd:INTERNAL=ON
//Number of processors available to run parallel tests.
VTK_MPI_NUMPROCS:INTERNAL=2
//ADVANCED property for variable: Vulkan_GLSLANG_VALIDATOR_EXECUTABLE
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_GLSLC_EXECUTABLE
Vulkan_GLSLC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_INCLUDE_DIR
Vulkan_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_LIBRARY
Vulkan_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
__pkg_config_arguments_PC_libusb:INTERNAL=libusb-1.0
__pkg_config_checked_PC_libusb:INTERNAL=1
//ADVANCED property for variable: boost_atomic_DIR
boost_atomic_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_filesystem_DIR
boost_filesystem_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_iostreams_DIR
boost_iostreams_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_serialization_DIR
boost_serialization_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_system_DIR
boost_system_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: libusb_LIBRARIES
libusb_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_libusb_usb-1.0
pkgcfg_lib_PC_libusb_usb-1.0-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/opt/homebrew/Cellar/libusb/1.0.26/lib

