%YAML:1.0
---
calibration_method: "PnP+镜面估计方法"
R_mirror_to_normal: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -8.7181307748977666e-01, 2.3880913920426260e-02,
       -4.8925623130230023e-01, -1.9249591840126631e-01,
       -9.3515716645119384e-01, 2.9736576035889983e-01,
       -4.5043010480723611e-01, 3.5342718625665842e-01,
       8.1987922567780913e-01 ]
T_mirror_to_normal: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ 1.9860845183401804e+02, -1.7324033630752673e+01,
       -5.8638585464753250e+00 ]
average_reprojection_error: 2.2037681109282104e+02
camera_matrix_normal: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 5.6806769969151046e+03, 0., 3.0435396639623441e+03, 0.,
       5.6850079679967812e+03, 3.9917184728922821e+03, 0., 0., 1. ]
dist_coeffs_normal: !!opencv-matrix
   rows: 1
   cols: 14
   dt: d
   data: [ -1.1153764526616854e+01, 3.1682872058022756e+01,
       6.3121123431944598e-03, -2.0946252734591591e-03,
       6.3954491681607840e+02, -1.1319299481663233e+01,
       3.4052338055132857e+01, 6.2322927191826693e+02, 0., 0., 0., 0.,
       0., 0. ]
board_size_width: 11
board_size_height: 8
square_size: 10.
rotation_angle_degrees: 1.7348667875972146e+02
translation_distance_mm: 1.9944880074394527e+02
