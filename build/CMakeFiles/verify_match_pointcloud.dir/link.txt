/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/verify_match_pointcloud.dir/verify_match_pointcloud.cpp.o -o verify_match_pointcloud -F/Library/Developer/CommandLineTools/SDKs/MacOSX14.0.sdk/System/Library/Frameworks -iframework /opt/homebrew/lib  -Wl,-rpath,/usr/local/opencv_4_8_1/lib -Wl,-rpath,/Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib -Wl,-rpath,/usr/local/lib -Wl,-rpath,/opt/homebrew/lib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_img_hash.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_surface.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_keypoints.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_tracking.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_recognition.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_stereo.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_outofcore.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_people.dylib /opt/homebrew/lib/libflann_cpp.1.9.2.dylib /usr/local/opencv_4_8_1/lib/libopencv_world.4.8.1.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_registration.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_segmentation.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_features.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_filters.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_sample_consensus.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_ml.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_visualization.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_search.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_kdtree.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_io.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_octree.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libpcap.tbd /opt/homebrew/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.5.sdk/usr/lib/libz.tbd /usr/local/lib/libvtkChartsCore-9.3.9.3.dylib /usr/local/lib/libvtkInteractionImage-9.3.9.3.dylib /usr/local/lib/libvtkIOGeometry-9.3.9.3.dylib /usr/local/lib/libvtkIOPLY-9.3.9.3.dylib /usr/local/lib/libvtkRenderingLOD-9.3.9.3.dylib /usr/local/lib/libvtkViewsContext2D-9.3.9.3.dylib /usr/local/lib/libvtkViewsCore-9.3.9.3.dylib /usr/local/lib/libvtkRenderingContextOpenGL2-9.3.9.3.dylib /usr/local/lib/libvtkGUISupportQt-9.3.9.3.dylib /usr/local/lib/libvtkInteractionWidgets-9.3.9.3.dylib /usr/local/lib/libvtkFiltersModeling-9.3.9.3.dylib /usr/local/lib/libvtkInteractionStyle-9.3.9.3.dylib /usr/local/lib/libvtkFiltersExtraction-9.3.9.3.dylib /usr/local/lib/libvtkIOLegacy-9.3.9.3.dylib /usr/local/lib/libvtkIOCore-9.3.9.3.dylib /usr/local/lib/libvtkRenderingAnnotation-9.3.9.3.dylib /usr/local/lib/libvtkRenderingContext2D-9.3.9.3.dylib /usr/local/lib/libvtkRenderingFreeType-9.3.9.3.dylib /usr/local/lib/libvtkfreetype-9.3.9.3.dylib /usr/local/lib/libvtkRenderingOpenGL2-9.3.9.3.dylib /usr/local/lib/libvtkIOImage-9.3.9.3.dylib /usr/local/lib/libvtkzlib-9.3.9.3.dylib /usr/local/lib/libvtkRenderingHyperTreeGrid-9.3.9.3.dylib /usr/local/lib/libvtkImagingSources-9.3.9.3.dylib /usr/local/lib/libvtkImagingCore-9.3.9.3.dylib /usr/local/lib/libvtkRenderingUI-9.3.9.3.dylib /usr/local/lib/libvtkRenderingCore-9.3.9.3.dylib /usr/local/lib/libvtkCommonColor-9.3.9.3.dylib /usr/local/lib/libvtkFiltersSources-9.3.9.3.dylib /usr/local/lib/libvtkFiltersGeneral-9.3.9.3.dylib /usr/local/lib/libvtkCommonComputationalGeometry-9.3.9.3.dylib /usr/local/lib/libvtkFiltersGeometry-9.3.9.3.dylib /usr/local/lib/libvtkFiltersCore-9.3.9.3.dylib /usr/local/lib/libvtkCommonExecutionModel-9.3.9.3.dylib /usr/local/lib/libvtkCommonDataModel-9.3.9.3.dylib /usr/local/lib/libvtkCommonMisc-9.3.9.3.dylib /usr/local/lib/libvtkCommonTransforms-9.3.9.3.dylib /usr/local/lib/libvtkCommonMath-9.3.9.3.dylib /usr/local/lib/libvtkkissfft-9.3.9.3.dylib /usr/local/lib/libvtkglew-9.3.9.3.dylib -Xlinker -framework -Xlinker OpenGL -framework Cocoa /opt/homebrew/lib/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets /opt/homebrew/lib/QtOpenGL.framework/Versions/A/QtOpenGL /opt/homebrew/lib/QtWidgets.framework/Versions/A/QtWidgets /opt/homebrew/lib/QtGui.framework/Versions/A/QtGui /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore -framework IOKit -framework DiskArbitration -Xlinker -framework -Xlinker OpenGL -Xlinker -framework -Xlinker AGL -framework AppKit -framework ImageIO -framework Metal /usr/local/lib/libvtkCommonCore-9.3.9.3.dylib /usr/local/lib/libvtksys-9.3.9.3.dylib /Users/<USER>/work/CxxDep/SourceCode/prefix/pcl/lib/libpcl_common.dylib /opt/homebrew/lib/libboost_system-mt.dylib /opt/homebrew/lib/libboost_iostreams-mt.dylib /opt/homebrew/lib/libboost_filesystem-mt.dylib /opt/homebrew/lib/libboost_atomic-mt.dylib /opt/homebrew/lib/libboost_serialization-mt.dylib /opt/homebrew/Cellar/lz4/1.9.4/lib/liblz4.dylib /opt/homebrew/lib/libqhull_r.8.0.2.dylib -lm 
